<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80" fill="none">
<g filter="url(#filter0_i_1_129875)">
<rect x="4" y="4" width="72" height="72" rx="4" fill="url(#paint0_linear_1_129875)" fill-opacity="0.8"/>




</g>
<rect x="0.5" y="0.5" width="79" height="79" rx="3.5" stroke="url(#paint1_linear_1_129875)" stroke-opacity="0.6"/>
<defs>
<filter id="filter0_i_1_129875" x="4" y="4" width="72" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.968627 0 0 0 0 0.811765 0 0 0 0 0.623529 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_129875"/>
</filter>
<linearGradient id="paint0_linear_1_129875" x1="40" y1="4" x2="40" y2="76" gradientUnits="userSpaceOnUse">
<stop stop-color="#EC860F"/>
<stop offset="1" stop-color="#FFCD33"/>
</linearGradient>
<linearGradient id="paint1_linear_1_129875" x1="40" y1="0" x2="40" y2="80" gradientUnits="userSpaceOnUse">
<stop stop-color="#F7CF9F"/>
<stop offset="1" stop-color="#F7BBA1" stop-opacity="0.1"/>
<stop offset="1" stop-color="#F7CF9F" stop-opacity="0.1"/>
</linearGradient>
</defs>
</svg>