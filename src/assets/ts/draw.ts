export const drawStyle = [
  {
    id: "gl-draw-polygon-fill-inactive",

    type: "fill",

    filter: [
      "all",

      ["==", "active", "false"],

      ["==", "$type", "Polygon"],

      ["!=", "user_type", "overlay"],

      ["!=", "mode", "static"],

      ["!=", "user_isSnap", "true"]
    ],

    paint: {
      "fill-color": "#3bb2d0",

      "fill-outline-color": "#3bb2d0",

      "fill-opacity": 0.1
    }
  },

  {
    id: "gl-draw-polygon-fill-active",

    type: "fill",

    filter: [
      "all",

      ["==", "active", "true"],

      ["==", "$type", "Polygon"],

      ["!=", "user_type", "overlay"]
    ],

    paint: {
      "fill-color": "#fbb03b",

      "fill-outline-color": "#fbb03b",

      "fill-opacity": 0.2
    }
  },

  {
    id: "gl-draw-overlay-polygon-fill-inactive",

    type: "fill",

    filter: [
      "all",

      ["==", "active", "false"],

      ["==", "$type", "Polygon"],

      ["==", "user_type", "overlay"],

      ["!=", "mode", "static"]
    ],

    paint: {
      "fill-color": "#3bb2d0",

      "fill-outline-color": "#3bb2d0",

      "fill-opacity": 0.01
    }
  },

  {
    id: "gl-draw-overlay-polygon-fill-active",

    type: "fill",

    filter: [
      "all",

      ["==", "active", "true"],

      ["==", "$type", "Polygon"],

      ["==", "user_type", "overlay"]
    ],

    paint: {
      "fill-color": "#fbb03b",

      "fill-outline-color": "#fbb03b",

      "fill-opacity": 0.01
    }
  },

  {
    id: "gl-draw-polygon-midpoint",

    type: "circle",

    filter: ["all", ["==", "$type", "Point"], ["==", "meta", "midpoint"]],

    paint: { "circle-radius": 5, "circle-color": "#fbb03b" }
  },

  {
    id: "gl-draw-polygon-stroke-inactive",

    type: "line",

    filter: [
      "all",

      ["==", "active", "false"],

      ["==", "$type", "Polygon"],

      ["!=", "mode", "static"],

      ["!=", "user_type", "overlay"],

      ["!=", "user_isSnap", "true"]
    ],

    layout: { "line-cap": "round", "line-join": "round" },

    paint: { "line-color": "#3bb2d0", "line-width": 4 }
  },

  {
    id: "gl-draw-polygon-stroke-active",

    type: "line",

    filter: ["all", ["==", "active", "true"], ["==", "$type", "Polygon"]],

    layout: { "line-cap": "round", "line-join": "round" },

    paint: {
      "line-color": "#fbb03b",

      "line-dasharray": [0.2, 2],

      "line-width": 3
    }
  },

  {
    id: "gl-draw-line-inactive",

    type: "line",

    filter: [
      "all",

      ["==", "active", "false"],

      ["==", "$type", "LineString"],

      ["!=", "mode", "static"],

      ["!=", "user_isSnap", "true"]
    ],

    layout: { "line-cap": "round", "line-join": "round" },

    paint: { "line-color": "#3bb2d0", "line-width": 4 }
  },

  {
    id: "gl-draw-line-active",

    type: "line",

    filter: ["all", ["==", "$type", "LineString"], ["==", "active", "true"]],

    layout: { "line-cap": "round", "line-join": "round" },

    paint: {
      "line-color": "#fbb03b",

      "line-dasharray": [0.2, 2],

      "line-width": 3
    }
  },

  {
    id: "gl-draw-polygon-and-line-vertex-stroke-inactive",

    type: "circle",

    filter: ["all", ["==", "meta", "vertex"], ["==", "$type", "Point"], ["!=", "mode", "static"]],

    paint: {
      "circle-radius": 5,

      "circle-color": "#fbb03b",

      "circle-stroke-width": 3,

      "circle-stroke-color": "#fff"
    }
  },

  {
    id: "gl-draw-point-inactive",

    type: "circle",

    filter: [
      "all",

      ["==", "active", "false"],

      ["==", "$type", "Point"],

      ["==", "meta", "feature"],

      ["!=", "mode", "static"]
    ],

    paint: {
      "circle-radius": 5,

      "circle-color": "#3bb2d0",

      "circle-stroke-width": 3,

      "circle-stroke-color": "#fff"
    }
  },

  {
    id: "gl-draw-point-active",

    type: "circle",

    filter: ["all", ["==", "$type", "Point"], ["==", "active", "true"], ["!=", "meta", "midpoint"]],

    paint: {
      "circle-radius": 7,

      "circle-color": "#fbb03b",

      "circle-stroke-width": 2,

      "circle-stroke-color": "#fff"
    }
  },

  {
    id: "gl-draw-polygon-fill-static",

    type: "fill",

    filter: ["all", ["==", "mode", "static"], ["==", "$type", "Polygon"]],

    paint: {
      "fill-color": "#404040",

      "fill-outline-color": "#404040",

      "fill-opacity": 0.1
    }
  },

  {
    id: "gl-draw-polygon-and-line-vertex-scale-icon",

    type: "symbol",

    filter: [
      "all",

      ["==", "meta", "vertex"],

      ["==", "$type", "Point"],

      ["!=", "mode", "static"],

      ["has", "heading"]
    ],

    layout: {
      "icon-image": "scale",

      "icon-allow-overlap": true,

      "icon-ignore-placement": true,

      "icon-rotation-alignment": "map",

      "icon-rotate": ["get", "heading"]
    },

    paint: {
      "icon-opacity": 1.0,

      "icon-opacity-transition": {
        delay: 0,

        duration: 0
      }
    }
  },

  {
    id: "gl-draw-line-rotate-point",

    type: "line",

    filter: [
      "all",

      ["==", "meta", "midpoint"],

      ["==", "icon", "rotate"],

      ["==", "$type", "LineString"],

      ["!=", "mode", "static"]

      // ['==', 'active', 'true']
    ],

    layout: {
      "line-cap": "round",

      "line-join": "round"
    },

    paint: {
      "line-color": "#fbb03b",

      "line-dasharray": [0.2, 2],

      "line-width": 2
    }
  },

  {
    id: "gl-draw-polygon-rotate-point",

    type: "circle",

    filter: [
      "all",

      ["==", "meta", "midpoint"],

      ["==", "icon", "rotate"],

      ["==", "$type", "Point"],

      ["!=", "mode", "static"]
    ],

    paint: {
      "circle-radius": 2,

      "circle-color": "#fbb03b",

      "circle-stroke-width": 2,

      "circle-stroke-color": "#fff"
    }
  },

  {
    id: "gl-draw-polygon-rotate-point-icon",

    type: "symbol",

    filter: [
      "all",

      ["==", "meta", "midpoint"],

      ["==", "icon", "rotate"],

      ["==", "$type", "Point"],

      ["!=", "mode", "static"]
    ],

    layout: {
      "icon-image": "rotate",

      "icon-allow-overlap": true,

      "icon-ignore-placement": true,

      "icon-rotation-alignment": "map",

      "icon-rotate": ["get", "heading"]
    },

    paint: {
      "icon-opacity": 1.0,

      "icon-opacity-transition": {
        delay: 0,

        duration: 0
      }
    }
  }
];
