import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
dayjs.extend(utc)

export function formatUtc(utcString: string, format = 'YYYY-MM-DD HH:mm:ss') {
  const resultTime = dayjs.utc(utcString).utcOffset(8).format(format)
  return resultTime
}

export function formatVw(px: number) {
  return `${(px / 1920) * 100}vw`
}

/**
 * 构造树型结构数据
 * @param data 数据源
 * @param id 节点唯一字段名，默认 'id'
 * @param parentId 父节点字段名，默认 'parentId'
 * @param children 孩子节点字段名，默认 'children'
 */
export function handleTree<T extends Record<string, any>>(
  data: T[],
  id: string = 'id',
  parentId: string = 'parentId',
  children: string = 'children',
): T[] {
  const config = {
    id,
    parentId,
    childrenList: children,
  }

  const childrenListMap: Record<string | number, T> = {}
  const tree: T[] = []

  // 初始化每个节点的 children，并构建 id 映射
  for (const item of data) {
    const itemId = item[config.id]
    if (!item[config.childrenList]) {
      ;(item as any)[config.childrenList] = []
    }
    childrenListMap[itemId] = item
  }

  // 构建树结构
  for (const item of data) {
    const parentKey = item[config.parentId]
    const parent = childrenListMap[parentKey]

    if (parent && parent !== item) {
      // 添加到父节点的 children 中
      parent[config.childrenList].push(item)
    } else {
      // 顶级节点
      tree.push(item)
    }
  }

  return tree
}
