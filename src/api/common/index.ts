import request from '@/service/request'

// 根据pid获取数据列表
export function getDataList(pid: string) {
  return request.get({
    url: `/attribute-dict/list/${pid}`,
  })
}

// 获取数据树形列表
export function getDataTreeList(query: any = {}) {
  return request.get({
    url: '/attribute-dict',
    params: query,
  })
}

// 获取类别详情
export function getAttributeDetail(pid: string) {
  return request.get({
    url: `/attribute-dict/list/${pid}`,
  })
}

// 获取列表数据
export function getFeatureList(query: any) {
  return request.get({
    url: '/feature',
    params: query,
  })
}

// 数据表格数据详情
export function getFeatureDetail(id: string) {
  return request.get({
    url: `/feature/${id}`,
  })
}

// 新增数据
export function addData(data: any) {
  return request.post({
    url: '/attribute-dict',
    data,
  })
}

// 获取浙江省code
export function getCityCodeApi() {
  return request.get({
    url: '/city',
  })
}

// 根据code获取范围框
export function useCodeObtainRange(sign: number) {
  return request.get({
    url: `/city/box/${sign}`,
  })
}

// 将文件转为 wkt串
export function fileConvertWkt(data: any) {
  return request.post({
    url: '/resource-file/transform',
    data,
  })
}

// 数据查询
export function dataSearch(data: any) {
  return request.post({
    url: '/feature/page',
    data,
  })
}

// 获取数据详情
export function getDetailData(id: string) {
  return request.get({
    url: `/feature/${id}`,
  })
}

// 搜索属性列表
export function searchAttributeList(query: any) {
  return request.get({
    url: '/attribute-dict/condition',
    params: query,
  })
}
