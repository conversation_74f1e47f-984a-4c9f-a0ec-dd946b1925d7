<template>
  <div class="left">
    <div class="nav-box" style="position: relative">
      <!-- <div class="nav-title">成果超市</div> -->
      <div class="nav-title">数据成果</div>
    </div>
    <div class="left-content">
      <div class="left-box">
        <!-- <div class="select-box">
          <div class="select-item select-item-check">
            <span>数据成果</span>
          </div>
          <div class="select-item">
            <span>最新成果</span>
          </div>
        </div> -->
        <div class="tree-box">
          <el-scrollbar>
            <el-tree
              style="max-width: 600px"
              :data="treeList"
              :props="{ children: 'children', label: 'attributeName', value: 'attributeId' }"
              node-key="attributeId"
              :render-content="renderContent"
              @node-click="clickHandler"
            />
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElIcon, type RenderContentFunction } from 'element-plus'
import { Document, FolderOpened } from '@element-plus/icons-vue'
import { useCommonStore } from '@/stores/common'
import { storeToRefs } from 'pinia'
import { getFeatureList, getAttributeDetail } from '@/api/common'
import { useEventBus } from '@vueuse/core'

const commonStore = useCommonStore()
const {
  treeList,
  tableList,
  attributeList,
  tableTotal,
  currentTreeNode,
  isTable,
  keyword,
  vagueLabel,
  dictList,
} = storeToRefs(commonStore)

const clickTreeBus = useEventBus('clickTree')

const renderContent: RenderContentFunction = (h, { node, data }) => {
  return h(
    'div',
    {
      class: 'el-tree-node__label',
    },
    [
      h(
        'div',
        {
          class: 'custom-tree-node',
        },
        [
          h(
            'div',
            {
              style: {
                display: 'flex',
                'align-items': 'center',
              },
            },
            [
              data?.children?.length
                ? h(ElIcon, null, () => h(FolderOpened))
                : h(ElIcon, null, () => h(Document)),
              h(
                'span',
                {
                  style: {
                    'margin-left': '10px',
                  },
                },
                node.label,
              ),
            ],
          ),
          h('div', null, [
            h('div', { class: 'is-focus' }, [
              data?.children?.length
                ? h('i', { class: 'iconfont icon-jiantou_yemian_xiangyou' })
                : null,
            ]),
          ]),
        ],
      ),
    ],
  )
}

async function clickHandler(data: any) {
  if (data.children.length) return
  // 保存当前选中的树节点信息
  currentTreeNode.value = data

  const list = await getAttributeDetail(data.attributeId)
  attributeList.value = list.data
  const res = await getFeatureList({
    typeGroup: data.attributeId,
    vagueLabel: vagueLabel.value,
    vagueValue: keyword.value,
  })

  const tableData = res.rows?.map((item: any) => {
    return {
      ...item,
      ...JSON.parse(item.validProperties),
    }
  })

  tableList.value = tableData || []
  tableTotal.value = res.total || 0

  const typeGroup = commonStore
    .findPathToRootIterativeAction(data.attributeId, dictList.value)
    .join(',')

  clickTreeBus.emit(typeGroup)

  isTable.value = true
}
</script>

<style scoped lang="scss">
.left {
  height: calc(100% - 60px);
  /* margin: 20px; */
  margin-right: 0;
  background: linear-gradient(180deg, rgba(22, 35, 65, 0.79) 75%, rgba(0, 0, 0, 0.15));
  .nav-box {
    display: flex;
    width: 98%;
    height: 38px;
    padding-left: 20px;
    background: url(../../../assets/img/title.png) no-repeat;
    background-size: 100% 100%;
    align-items: center;
    justify-content: space-between;
    line-height: 38px;
    margin: -10px 0 0 0;
    color: #fff;
    .nav-title {
      font-family: FZDHTJW;
      font-weight: 700;
      font-size: 24px;
      padding-left: 28px;
    }
  }
  .left-content {
    height: calc(100% - 40px);
    .left-box {
      display: flex;
      flex-direction: column;
      margin-bottom: 4px;
      width: 100%;
      height: 100%;
    }
    .select-box {
      display: flex;
      width: 100%;
      flex-wrap: nowrap;
      margin-top: 10px;
      padding: 0 24px;
      gap: 20px;
      .select-item {
        flex: 1;
        height: 32px;
        text-align: center;
        font-size: 16px;
        line-height: 32px;
        color: #fff;
        background-image: url(../../../assets/img/tab.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        cursor: pointer;
      }
      .select-item-check {
        background-image: url(../../../assets/img/tab-on.png);
      }
    }
    .tree-box {
      flex: 1;
      margin-top: 14px;
      height: 0;
      .el-tree {
        margin-left: 15px;
        background: transparent;
        :deep(.el-tree-node__content) {
          margin-bottom: 8px;
          height: 34px;
        }
        :deep(.el-tree-node__label) {
          color: #fff;
          width: 100%;
          height: 100%;
          font-size: 18px !important;
          border-radius: 3px;
          background: linear-gradient(
            90deg,
            rgba(79, 172, 254, 0.3),
            rgba(0, 242, 254, 0)
          ) !important;
        }
        :deep(.el-tree-node__content:hover) {
          background: transparent;
        }
        :deep(.el-tree-node:focus > .el-tree-node__content) {
          background: transparent;
          transform: all 0.3s;
        }
        :deep(.el-tree-node.is-current > .el-tree-node__content .el-tree-node__label) {
          background: url(../../../assets/img/isChoose.png) no-repeat !important;
          background-size: 103.5% 100% !important;
        }
        :deep(.el-tree-node__label:hover) {
          background: url(../../../assets/img/isChoose.png) no-repeat !important;
          background-size: 103.5% 100% !important;
        }

        :deep(.is-expanded > .el-tree-node__content .is-focus) {
          transform: rotate(90deg);
          padding-right: 3px;
        }

        :deep(.icon-jiantou_yemian_xiangyou::before) {
          font-family: 'iconfont';
          content: '\eb0b';
        }

        :deep(.el-tree-node__expand-icon) {
          display: none;
        }
        :deep(.custom-tree-node) {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          /* padding: 12px; */
          padding-right: 8px;
          padding-left: 12px;
        }
      }
      :deep(.el-scrollbar__thumb) {
        background-color: #80e9ff;
      }
    }
  }
}
</style>
