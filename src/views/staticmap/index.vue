<template>
  <div class="static-map">
    <div class="header">
      <!-- <img :src="headerImg" alt="" /> -->
      浙江测绘质检资源库数据查询服务
    </div>

    <div class="content">
      <div class="left-content">
        <Left />
      </div>
      <div class="center-content">
        <div
          class="center"
          :style="{
            right: commonStore.isSearch ? '1%' : '-39%',
          }"
        >
          <Center />
        </div>
        <div class="table" v-if="commonStore.isTable">
          <Table />
        </div>
      </div>
      <div class="right-content">
        <Right v-show="commonStore.isSearch" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Left from './left/index.vue'
import Right from './right/index.vue'
import Center from './center/index.vue'
import Table from '@/views/table/index.vue'

// import { headerImg } from '@/utils/base64'
import { useCommonStore } from '@/stores/common'

const commonStore = useCommonStore()
</script>

<style scoped lang="scss">
.static-map {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: url(../../assets/img/map-home-bg.png) no-repeat;
  background-size: 100% 100%;
  /* background-color: #6e7a70; */

  .header {
    display: flex;
    height: 80px;
    align-items: center;
    justify-content: center;
    font-size: 34px;
    color: #e2edfb;
    font-weight: 800;
    font-family: cursive;
    font-style: italic;
    letter-spacing: 6px;
    img {
      height: 30px;
    }
  }
  .content {
    position: relative;
    height: calc(100% - 100px);
    display: flex;
    /* justify-content: space-between; */
    padding: 20px;
    .left-content {
      width: 22%;
      pointer-events: all;
    }
    .right-content {
      width: 22%;
      display: flex;
    }
    .center-content {
      flex: 1;
      /* pointer-events: all; */
      position: relative;
      .center {
        position: absolute;
        top: 0;
        right: 0;
        pointer-events: all;
      }
      .table {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        pointer-events: all;
      }
    }
  }
}
</style>
