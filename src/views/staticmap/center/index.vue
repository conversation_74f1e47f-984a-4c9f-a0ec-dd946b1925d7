<template>
  <div class="right">
    <div class="right-box">
      <template v-for="(item, index) in controlList" :key="item.name">
        <div
          :class="['box', { active: currentIndex === index }]"
          @click="showControlBox(item, index)"
        >
          <IconSvg :icon-class="item.icon" class-name="icon" />
          <div class="title">{{ item.name }}</div>
        </div>
      </template>
    </div>

    <div
      class="location"
      :style="{
        top: formatVw(location?.top ?? 0),
        right: formatVw(location?.right ?? 0),
      }"
      v-if="isShow"
    >
      <div class="popup-box">
        <div class="city-header">
          <div class="title">
            <IconSvg icon-class="location" />
            <span>当前位置：</span>
            <template v-for="(item, index) in currentLocation" :key="item.id">
              <div class="location-item" @click="clickCityHandler(item, index)">
                <span>{{ item.name }}</span>
                <span v-if="index < currentLocation.length - 1">&nbsp;>&nbsp;</span>
              </div>
            </template>
          </div>
          <el-icon style="cursor: pointer" @click="closeHandler"><Close /></el-icon>
        </div>
        <div class="city-box">
          <template v-for="item in currentCitys" :key="item">
            <div class="city" @click="addCityHandler(item)">
              {{ item.name }}
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import IconSvg from '@/components/SvgIcon/index.vue'
import { Close } from '@element-plus/icons-vue'
import { formatVw } from '@/utils/format'
import { useCommonStore } from '@/stores/common'
import { storeToRefs } from 'pinia'

import { useEventBus } from '@vueuse/core'
import { useCodeObtainRange } from '@/api/common'

const rangeBus = useEventBus<string>('range')
const clearBus = useEventBus('clear')

const commonStore = useCommonStore()
const { currentLocation, currentCitys, isSearch, isDetail, isShow } = storeToRefs(commonStore)

const controlList = ref([
  {
    icon: 'quhua',
    name: '区划',
    location: {
      top: 0,
      right: 102,
    },
  },
  // {
  //   icon: 'dizhi',
  //   name: '地址搜索',
  //   location: {
  //     top: 82,
  //     right: 102,
  //   },
  // },
  {
    icon: 'sousuo',
    name: '查询',
  },
  {
    icon: 'base',
    name: '底图',
  },
  {
    icon: 'qingkong',
    name: '清空',
  },
  // {
  //   icon: 'history',
  //   name: '历史影像',
  // },
])

const currentIndex = ref(null)
const location = ref({
  top: 0,
  right: 102,
})

// const isShow = ref(false)

function showControlBox(item: any, index: any) {
  const name = item.name
  if (name === '区划' || name === '地址搜索') {
    isShow.value = currentIndex.value !== index

    // location.value = item.location
  } else if (item.name === '查询') {
    isSearch.value = true
    isDetail.value = false

    if (currentIndex.value === index) {
      isSearch.value = false
    }
    isShow.value = false
  }
  currentIndex.value = currentIndex.value === index ? null : index
  if (item.name === '清空') {
    isShow.value = false
    currentIndex.value = null
    clearBus.emit()
  }
}

function closeHandler() {
  isShow.value = false
  currentIndex.value = null
}

function clickCityHandler(item: any, index: number) {
  currentLocation.value = currentLocation.value.splice(0, index + 1)
  commonStore.getCurrentCitysAction(item.id)
  if (item.name === '浙江省') {
    rangeBus.emit('zjs')
  } else {
    getRange(item)
  }
}

function addCityHandler(item: any) {
  currentLocation.value.push(item)
  commonStore.getCurrentCitysAction(item.id)
  getRange(item)
}

async function getRange(item: any) {
  const res = await useCodeObtainRange(item.id)
  rangeBus.emit(res.data[0])
}

watch(isSearch, (value) => {
  if (!value) currentIndex.value = null
  isShow.value = false
})
</script>

<style scoped lang="scss">
.right {
  /* display: flex; */
  .right-box {
    .box {
      display: flex;
      width: 82px;
      height: 82px;
      background-image: url(../../../assets/img/toolbar.svg);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: hsla(0, 0%, 100%, 0.85);
      text-align: center;
      font-size: 16px;
      cursor: pointer;
      user-select: none;
      .icon {
        margin-bottom: 4px;
        width: 26px;
        height: 26px;
      }
    }
    .active {
      background-image: url(../../../assets/img/toolbar-on.svg);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      color: #fff;
      .icon {
        color: #fff;
      }
    }
  }
  .location {
    position: absolute;
    font-size: 18px;
    color: #fff;
    background-color: rgba(5, 46, 123, 0.6);
    padding: 10px;
    /* min-width: 500px; */
    z-index: 98;
    .popup-box {
      .city-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 36px;
        border-bottom: 1px solid hsla(0, 0%, 100%, 0.639);
        .title {
          display: flex;
          align-items: center;
          color: #fed54f;
          .location-item {
            display: flex;
            align-items: center;
            cursor: pointer;
          }
        }
      }
      .city-box {
        display: flex;
        flex-wrap: wrap;
        width: 460px;
        .city {
          padding: 6px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
