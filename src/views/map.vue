<template>
  <div class="main">
    <div class="map" ref="mapRef"></div>
    <div class="robot" @click="showRobotDialog">
      <img src="../assets/img/robot.png" alt="AI助手" />
    </div>
    <div class="search">
      <el-input
        v-model="keyword"
        placeholder="搜索数据..."
        clearable
        @keyup.enter="handleSearch"
      ></el-input>
    </div>
    <div class="static">
      <static-map />
      <div class="zoom-num">当前级别：{{ mapLevel }}</div>
    </div>

    <!-- AI聊天框 -->
    <RobotDialog v-model:visible="robotDialogVisible" @close="handleCloseRobotDialog" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import mapboxDraw, { type DrawCreateEvent } from '@mapbox/mapbox-gl-draw'
import '@mapbox/mapbox-gl-draw/dist/mapbox-gl-draw.css'
import { mapbox_map_style } from '@/assets/ts/map_style'
import { drawStyle } from '@/assets/ts/draw'
import StaticMap from './staticmap/index.vue'
import RobotDialog from './robotDialog/index.vue'
import { useCommonStore } from '@/stores/common'
import { storeToRefs } from 'pinia'
import { useRoute } from 'vue-router'
import { LOGIN_TOKEN } from '@/global/constants'
import { localCache } from '@/utils/cache'
import { wktToGeoJSON, geojsonToWKT } from '@terraformer/wkt'
import * as turf from '@turf/turf'

import { useEventBus } from '@vueuse/core'
import axios from 'axios'
import { getDataTreeList } from '@/api/common'
import { handleTree } from '@/utils/format'
const rangeBus = useEventBus<string>('range')
const drawBus = useEventBus('draw')
const clearBus = useEventBus('clear')

const uploadBus = useEventBus('upload')

const selectBus = useEventBus('select')
const searchBus = useEventBus('search')

const clickTreeBus = useEventBus('clickTree')

mapboxgl.accessToken =
  'pk.eyJ1Ijoic3o2NjY2NjYiLCJhIjoiY2tuam44NXZzMDEwMzJ1cGV3djR6OHA5cCJ9.2LA3YOPHRLTTB25CvAoIdw'

const commonStore = useCommonStore()
const {
  mapLevel,
  currentLocation,
  geometryWkt,
  selectGeometry,
  selectId,
  treeList,
  keyword,
  vagueLabel,
} = storeToRefs(commonStore)

const route = useRoute()

const { token } = route.query
localCache.setCache(LOGIN_TOKEN, token)

commonStore.getTreeAction()
commonStore.getCityCodeAction()

let _map: any = null
const map = ref()
const mapRef = ref()

// AI聊天框相关
const robotDialogVisible = ref(false)

// const modes = mapboxDraw.modes
const draw = new mapboxDraw({
  // modes: modes,
  displayControlsDefault: false,
  userProperties: true,
  styles: drawStyle,
})

const importImg = (name: string) => {
  return new URL(`../assets/img/${name}.png`, import.meta.url).href
}

onMounted(() => {
  rangeBus.on((geometry) => {
    let zoom = 8

    if (geometry === 'zjs') {
      _map.getSource('range').setData({
        type: 'FeatureCollection',
        features: [],
      })

      _map.jumpTo({
        center: [119.754400955, 28.486214067],
        zoom,
      })
    } else {
      const polygon = wktToGeoJSON(geometry)

      _map.getSource('range').setData({
        type: 'FeatureCollection',
        features: [
          {
            geometry: polygon,
          },
        ],
      })

      const center = turf.centroid(polygon)

      const length = currentLocation.value.length
      if (length === 4) {
        zoom = 12
      } else if (length === 3) {
        zoom = 11
      }
      _map.jumpTo({
        center: center.geometry.coordinates,
        zoom,
      })
    }
  })

  drawBus.on(() => {
    clearLayer()
    hideLayer()
    draw.changeMode('draw_polygon')
  })

  selectBus.on(() => {
    clearLayer()

    _map.getSource('highlights').setData({
      type: 'FeatureCollection',
      features: [
        {
          geometry: selectGeometry.value,
        },
      ],
    })
    const center = turf.centroid(selectGeometry.value)
    _map.flyTo({
      center: center.geometry.coordinates,
    })
  })
  uploadBus.on(() => {
    const geometry = wktToGeoJSON(geometryWkt.value)
    _map.getSource('draw').setData({
      type: 'FeatureCollection',
      features: [
        {
          geometry,
        },
      ],
    })
  })

  searchBus.on((e: any) => {
    clearLayer()
    hideLayer()
    const { typeGroup, wktGroup, citySign, params } = e

    const query: any = {
      typeGroup,
      wktGroup,
      citySign,
    }
    for (const key in params) {
      query[key] = params[key]
    }

    const layers = ['allData-fill', 'allData-line', 'allData-point']
    layers.forEach((layer) => {
      _map.setLayoutProperty(layer, 'visibility', 'visible')
    })

    _map
      .getSource('allData')
      .setTiles([getUrl(import.meta.env.VITE_APP_BASE_API, 'feature/allData/{z}/{x}/{y}', query)])
      .reload()
  })

  clickTreeBus.on((typeGroup: any) => {
    clearLayer()
    const layers = ['allData-fill', 'allData-line', 'allData-point']
    layers.forEach((layer) => {
      _map.setLayoutProperty(layer, 'visibility', 'visible')
    })
    const query = {
      typeGroup,
    }
    _map
      .getSource('allData')
      .setTiles([getUrl(import.meta.env.VITE_APP_BASE_API, 'feature/allData/{z}/{x}/{y}', query)])
      .reload()
  })

  clearBus.on(() => {
    clearLayer()
    hideLayer()
  })

  initMap()
})

function initMap() {
  const imgList = [
    {
      name: 'point',
      src: importImg('point'),
    },
    {
      name: 'point-on',
      src: importImg('point-on'),
    },
  ]
  map.value = new mapboxgl.Map({
    container: mapRef.value,
    style: mapbox_map_style as any,
    // style: {
    //   version: 8,
    //   name: 'Tianditu IMG',
    //   sources: {
    //     'tdt-img': {
    //       type: 'raster',
    //       tiles: [
    //         'http://t6.tianditu.gov.cn/DataServer?T=img_w&X={x}&Y={y}&L={z}&tk=599a5d9e2bf2658021b3f431d213290a',
    //       ],
    //       tileSize: 256,
    //     },
    //   },
    //   layers: [
    //     {
    //       id: 'tdt-img-layer',
    //       type: 'raster',
    //       source: 'tdt-img',
    //     },
    //   ],
    // },
    center: [119.754400955, 28.486214067], // 地图起始位置
    zoom: 8, // 地图起始比列
    maxZoom: 20,
    minZoom: 7,
    // 在这里添加 transformRequest
    transformRequest: (url, resourceType) => {
      // 判断是否是我们自己的瓦片请求
      if (resourceType === 'Tile' && url.startsWith(import.meta.env.VITE_APP_BASE_API)) {
        return {
          // 返回一个包含完整 URL 的对象
          url: window.location.origin + url,
        }
      }
      // 对于其他请求（比如 Mapbox 官方的资源），不做修改
      return { url }
    },
  })
  _map = map.value

  _map.on('load', () => {
    // 加载图片
    imgList.forEach((item) => {
      _map.loadImage(item.src, (error: any, image: any) => {
        if (error) throw error
        if (!_map.hasImage(item.name)) {
          _map.addImage(item.name, image)
        }
      })
    })

    addSource(_map)
    addLayer(_map)

    _map.addControl(draw)
  })

  _map.on(
    'click',
    ['select-fill', 'select-line', 'select-point', 'allData-fill', 'allData-line', 'allData-point'],
    (e: any) => {
      console.log(e.features[0])
      // 1. 安全性检查：确保 e.features 存在且不为空
      if (!e.features || e.features.length === 0) {
        return
      }

      const clickedFeature = e.features[0]
      const layerId = clickedFeature.layer.id
      if (layerId.startsWith('allData-')) {
        selectId.value = clickedFeature.properties?.id
      }
      commonStore.getDetailDataAction().then((res) => {
        const geometry = JSON.parse(res.data.geometry)
        _map.getSource('highlights').setData({
          type: 'FeatureCollection',
          features: [{ geometry }],
        })
      })
    },
  )
  _map.on('mousemove', ['select-fill', 'select-line', 'select-point'], () => {
    _map.getCanvas().style.cursor = 'pointer'
  })

  // 监听绘制事件
  _map.on('draw.create', (e: DrawCreateEvent) => {
    console.log(e.features[0])
    geometryWkt.value = geojsonToWKT(e.features[0].geometry)

    _map.getSource('draw').setData({
      type: 'FeatureCollection',
      features: e.features,
    })
    draw.deleteAll()
  })

  const mapZoom: number = _map.getZoom()
  mapLevel.value = mapZoom.toFixed(2)

  //层级变化
  _map.on('zoom', () => {
    const mapZoom: number = _map.getZoom()
    mapLevel.value = mapZoom.toFixed(2)
  })
}

function addSource(_map: any) {
  // 全量数据切片
  _map.addSource('allData', {
    type: 'vector',
    tiles: [`${import.meta.env.VITE_APP_BASE_API}/feature/allData/{z}/{x}/{y}`],
    promoteId: 'id',
  })

  // 区划数据
  _map.addSource('range', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  })
  // 绘制数据
  _map.addSource('draw', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  })
  // 表格选中数据
  _map.addSource('select', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  })
  // 高亮数据
  _map.addSource('highlights', {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  })
}

function addLayer(_map: any) {
  // 切片图层
  _map.addLayer({
    id: 'allData-fill',
    type: 'fill',
    source: 'allData',
    'source-layer': 'allData',
    paint: {
      'fill-color': '#ffff54',
      'fill-opacity': 0.3,
    },
    layout: { visibility: 'none' },
  })
  _map.addLayer({
    id: 'allData-line',
    type: 'line',
    source: 'allData',
    'source-layer': 'allData',
    paint: {
      'line-color': '#ffff54',
      'line-width': 2,
    },
    layout: { visibility: 'none' },
  })
  _map.addLayer({
    id: 'allData-point',
    type: 'symbol',
    source: 'allData',
    'source-layer': 'allData',
    layout: {
      'icon-image': 'point',
      'icon-size': 0.7,
      visibility: 'none',
    },
    filter: ['==', '$type', 'Point'],
  })

  _map.addLayer({
    id: 'range-box-fill',
    type: 'fill',
    source: 'range',
    paint: {
      'fill-color': '#ffff54',
      'fill-opacity': 0.3,
    },
  })
  _map.addLayer({
    id: 'range-box-line',
    type: 'line',
    source: 'range',
    paint: {
      'line-color': '#ffff54',
      'line-width': 2,
    },
  })
  _map.addLayer({
    id: 'draw-line',
    type: 'line',
    source: 'draw',
    paint: {
      'line-color': '#ffff55',
      'line-width': 2,
    },
  })

  _map.addLayer({
    id: 'select-fill',
    type: 'fill',
    source: 'select',
    paint: {
      'fill-color': '#ffff54',
      'fill-opacity': 0.3,
    },
    filter: ['==', '$type', 'Polygon'],
  })
  _map.addLayer({
    id: 'select-line',
    type: 'line',
    source: 'select',
    paint: {
      'line-color': '#ffff54',
      'line-width': 2,
    },
  })
  _map.addLayer({
    id: 'select-point',
    type: 'symbol',
    source: 'select',
    layout: {
      'icon-image': 'point',
      'icon-size': 0.7,
    },
    filter: ['==', '$type', 'Point'],
  })

  // 高亮图层
  _map.addLayer({
    id: 'highlights-fill',
    type: 'fill',
    source: 'highlights',
    paint: {
      'fill-color': '#beb0c3',
      'fill-opacity': 0.7,
    },
    filter: ['==', '$type', 'Polygon'],
  })
  _map.addLayer({
    id: 'highlights-line',
    type: 'line',
    source: 'highlights',
    paint: {
      'line-color': '#beb0c3',
      'line-width': 2,
    },
  })
  _map.addLayer({
    id: 'highlights-point',
    type: 'symbol',
    source: 'highlights',
    layout: {
      'icon-image': 'point-on',
      'icon-size': 0.7,
    },
    filter: ['==', '$type', 'Point'],
  })
}

function getUrl(baseURL: string, url: string, query: any) {
  const config = {
    baseURL,
    url,
    params: query,
    method: 'get',
  }
  return axios.getUri(config)
}

function clearLayer() {
  const sources = ['range', 'draw', 'select', 'highlights']
  sources.forEach((source) => {
    _map.getSource(source).setData({
      type: 'FeatureCollection',
      features: [],
    })
  })
}

function hideLayer() {
  const layers = ['allData-fill', 'allData-line', 'allData-point']
  layers.forEach((layer) => {
    _map.setLayoutProperty(layer, 'visibility', 'none')
  })
}

async function handleSearch() {
  const res = await getDataTreeList({ keyword: keyword.value })
  const treeData = handleTree(res.data, 'attributeId', 'attributePid')
  treeList.value = commonStore.pruneTreeBottomUpAction(treeData)

  const layers = ['allData-fill', 'allData-line', 'allData-point']
  layers.forEach((layer) => {
    _map.setLayoutProperty(layer, 'visibility', 'visible')
  })

  _map
    .getSource('allData')
    .setTiles([
      `${import.meta.env.VITE_APP_BASE_API}/feature/allData/{z}/{x}/{y}?vagueValue=${keyword.value}&vagueLabel=${vagueLabel.value}`,
    ])
    .reload()
}

// AI聊天框相关方法
function showRobotDialog() {
  robotDialogVisible.value = true
}

function handleCloseRobotDialog() {
  robotDialogVisible.value = false
}
</script>

<style scoped lang="scss">
.main {
  position: relative;
  width: 100%;
  height: 100%;
  .map {
    width: 100%;
    height: 100%;
  }
  .search {
    position: absolute;
    top: 5%;
    right: 10%;
    z-index: 999;
    padding: 5px;
    background: rgba(22, 35, 65, 0.79);
  }
  .robot {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 999;
    padding: 5px;
    width: 68px;
    height: 68px;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .static {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    .zoom-num {
      position: absolute;
      top: 7.5vh;
      left: 50%;
      transform: translateX(-50%);
      padding: 3px 10px;
      background: rgba(0, 0, 0, 0.4);
      color: #fff;
    }
  }

  :deep(.mapboxgl-ctrl) {
    display: none !important;
  }
}
</style>
