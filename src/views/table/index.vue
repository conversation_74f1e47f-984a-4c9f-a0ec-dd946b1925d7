<template>
  <div class="table-box">
    <!-- 表格标题和统计信息 -->
    <div class="table-title">
      <div class="title-text">{{ currentTreeNode?.attributeName || '数据成果列表' }}</div>
      <div class="title-actions">
        <!-- <div class="search-box" v-if="tableList.length > 0">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索数据名称..."
            size="small"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div> -->
        <div class="total-count">共 {{ filteredTableList.length }} / {{ tableTotal }} 条数据</div>
        <div class="close-btn" @click="handleClose">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="table-wrapper" v-if="tableList.length > 0">
      <el-scrollbar
        class="table-scrollbar"
        ref="scrollbarRef"
        @scroll="handleScroll"
        :always="true"
      >
        <table class="data-table">
          <thead>
            <tr>
              <th class="table-header index-header">序号</th>
              <th v-for="attr in sortedAttributeList" :key="attr.attributeId" class="table-header">
                <div class="header-content">{{ attr.attributeName }}</div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(item, index) in displayTableList"
              :key="item.id || index"
              class="table-row"
              @click="handleRowClick(item)"
            >
              <td class="table-cell index-cell">{{ index + 1 }}</td>
              <td
                v-for="attr in sortedAttributeList"
                :key="attr.attributeId"
                class="table-cell"
                :title="item[attr.attributeLabel]"
              >
                <span class="cell-content">
                  {{ formatCellValue(item[attr.attributeLabel]) }}
                </span>
              </td>
            </tr>
            <!-- 加载更多指示器 -->
            <tr v-if="isLoading" class="loading-row">
              <td :colspan="sortedAttributeList.length + 1" class="loading-cell">
                <div class="loading-content">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <span>加载中...</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </el-scrollbar>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📊</div>
      <div class="empty-text">暂无数据</div>
      <div class="empty-desc">请选择左侧分类查看数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Search, Close, Loading } from '@element-plus/icons-vue'
import { useCommonStore } from '@/stores/common'
import { storeToRefs } from 'pinia'
import { getFeatureDetail } from '@/api/common'
import { useEventBus } from '@vueuse/core'

const commonStore = useCommonStore()
const {
  tableTotal,
  attributeList,
  tableList,
  currentTreeNode,
  isLoading,
  selectGeometry,
  selectId,
} = storeToRefs(commonStore)

const selectBus = useEventBus('select')

// 搜索关键词
const searchKeyword = ref('')

// 滚动容器引用
const scrollbarRef = ref()

// 显示的数据条数
const displayCount = ref(10)

// 按照指定顺序排序属性列表
const sortedAttributeList = computed(() => {
  if (!attributeList.value || attributeList.value.length === 0) return []

  // 按照 sort 字段排序
  return [...attributeList.value].sort((a, b) => (a.sort || 0) - (b.sort || 0))
})

// 过滤后的表格数据
const filteredTableList = computed(() => {
  if (!searchKeyword.value.trim()) {
    return tableList.value
  }

  return tableList.value.filter((item) => {
    const keyword = searchKeyword.value.toLowerCase()
    return (
      (item.dataName && item.dataName.toLowerCase().includes(keyword)) ||
      (item.dataType && item.dataType.toLowerCase().includes(keyword)) ||
      (item.Datasource && item.Datasource.toLowerCase().includes(keyword)) ||
      (item.Storagepath && item.Storagepath.toLowerCase().includes(keyword))
    )
  })
})

// 显示的表格数据（用于滚动加载）
const displayTableList = computed(() => {
  return filteredTableList.value.slice(0, displayCount.value)
})

// 格式化单元格值
const formatCellValue = (value: any) => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'string' && value.trim() === '') return '-'

  // 特殊格式化处理
  if (typeof value === 'string') {
    // 如果是日期格式，可以进行格式化
    if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
      return value
    }
    // 限制显示长度，避免过长文本
    return value.length > 20 ? value.substring(0, 20) + '...' : value
  }

  return String(value)
}

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已经在 computed 中处理，这里可以添加额外的搜索逻辑
  console.log('搜索关键词:', searchKeyword.value)
  // 重置显示条数
  displayCount.value = 10
}

// 处理滚动事件
const handleScroll = ({ scrollTop }: { scrollTop: number }) => {
  const scrollbar = scrollbarRef.value
  if (!scrollbar) return

  const { clientHeight, scrollHeight } = scrollbar.wrapRef

  // 当滚动到底部附近时加载更多数据
  if (scrollTop + clientHeight >= scrollHeight - 50) {
    loadMoreData()
  }
}

// 加载更多数据
const loadMoreData = () => {
  if (isLoading.value || displayCount.value >= filteredTableList.value.length) {
    return
  }

  isLoading.value = true

  // 模拟异步加载
  setTimeout(() => {
    displayCount.value += 10
    isLoading.value = false
  }, 500)
}

// 处理关闭按钮
const handleClose = () => {
  commonStore.isTable = false
}

// 处理行点击事件
const handleRowClick = async (item: any) => {
  const res = await getFeatureDetail(item.id)
  selectId.value = item.id
  selectGeometry.value = JSON.parse(res.data.geometry)
  selectBus.emit()
  // 这里可以添加行点击的业务逻辑，比如显示详情弹窗等
}
</script>

<style scoped lang="scss">
.table-box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: url(../../assets/img/table-bg.png) no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
  color: #fff;
  pointer-events: all;
  overflow: hidden;

  // 表格标题区域
  .table-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);

    .title-text {
      font-size: 22px;
      font-weight: 700;
      color: #fff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .title-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .search-box {
        :deep(.el-input) {
          width: 200px;

          .el-input__wrapper {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(128, 233, 255, 0.3);
            border-radius: 20px;
            box-shadow: none;

            &:hover {
              border-color: rgba(128, 233, 255, 0.5);
            }

            &.is-focus {
              border-color: #80e9ff;
              box-shadow: 0 0 0 2px rgba(128, 233, 255, 0.2);
            }
          }

          .el-input__inner {
            color: #fff;
            font-size: 14px;

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
            }
          }

          .el-input__prefix {
            color: rgba(255, 255, 255, 0.6);
          }

          .el-input__suffix {
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }

      .total-count {
        font-size: 16px;
        color: #80e9ff;
        background: rgba(128, 233, 255, 0.1);
        padding: 6px 12px;
        border-radius: 15px;
        border: 1px solid rgba(128, 233, 255, 0.3);
        white-space: nowrap;
      }

      .close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
          transform: scale(1.1);
        }

        .el-icon {
          color: #fff;
          font-size: 16px;
        }
      }
    }
  }

  // 表格容器
  .table-wrapper {
    height: 225px; // 固定高度而不是最大高度
    overflow: hidden;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    position: relative; // 确保定位稳定

    .table-scrollbar {
      height: 100%;
      width: 100%;

      // 确保滚动容器稳定
      :deep(.el-scrollbar__wrap) {
        overflow-x: auto;
        overflow-y: auto;
      }

      :deep(.el-scrollbar__view) {
        display: block;
        width: 100%;
      }

      :deep(.el-scrollbar__thumb) {
        background-color: #80e9ff;
        border-radius: 4px;
        opacity: 0.6;

        &:hover {
          opacity: 0.9;
        }
      }

      :deep(.el-scrollbar__bar) {
        opacity: 1; // 始终显示滚动条轨道

        &.is-vertical {
          right: 2px;
          width: 6px;
        }
        &.is-horizontal {
          bottom: 2px;
          height: 6px;
        }
      }

      :deep(.el-scrollbar__track) {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        opacity: 1; // 始终显示轨道
      }
    }

    // 表格样式
    .data-table {
      width: 100%;
      min-width: 1000px; // 设置最小宽度确保横向滚动
      border-collapse: collapse;
      font-size: 14px;
      position: relative; // 确保表格定位稳定
      transform: translateZ(0); // 启用硬件加速，减少重绘

      thead {
        position: sticky;
        top: 0;
        z-index: 100;

        .table-header {
          background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
          color: #fff;
          font-weight: 700;
          font-size: 16px;
          padding: 15px 12px;
          text-align: center;
          border: 1px solid rgba(255, 255, 255, 0.1);
          position: sticky;
          top: 0;
          z-index: 101;
          white-space: nowrap; // 表头不换行
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); // 添加阴影确保层级

          // 添加背景遮罩确保完全覆盖
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            z-index: -1;
          }

          .header-content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            position: relative;
            z-index: 1;
          }

          &.index-header {
            width: 80px; // 固定序号列宽度
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #80e9ff, transparent);
            z-index: 1;
          }
        }
      }

      tbody {
        .table-row {
          transition: all 0.3s ease;
          cursor: pointer;
          position: relative;
          z-index: 1; // 确保行的层级低于表头

          &:nth-child(even) {
            background: rgba(255, 255, 255, 0.05);
          }

          &:hover {
            background: rgba(128, 233, 255, 0.15);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 2; // hover时稍微提高层级，但仍低于表头
          }

          .table-cell {
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;
            text-align: center;
            vertical-align: middle;
            white-space: nowrap; // 单元格内容不换行

            &.index-cell {
              background: rgba(128, 233, 255, 0.1);
              font-weight: 600;
              color: #80e9ff;
              width: 80px;
            }

            .cell-content {
              display: inline-block;
              max-width: 100%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }

      // 加载更多样式
      .loading-row {
        position: relative; // 确保加载行不影响其他行的位置

        .loading-cell {
          padding: 20px;
          text-align: center;
          border: none;
          background: transparent;

          .loading-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #80e9ff;
            font-size: 14px;

            .el-icon {
              font-size: 16px;
              animation: rotate 2s linear infinite;
              transform-origin: center; // 确保旋转中心稳定
            }
          }
        }
      }
    }
  }

  // 空状态
  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: rgba(255, 255, 255, 0.6);

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-text {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .empty-desc {
      font-size: 14px;
      opacity: 0.7;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .table-box {
    .data-table {
      font-size: 12px;

      .table-header {
        font-size: 14px;
        padding: 12px 8px;
      }

      .table-cell {
        padding: 10px 8px;
      }
    }
  }
}

// 旋转动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
