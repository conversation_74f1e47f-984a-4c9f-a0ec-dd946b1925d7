<template>
  <div class="robot-dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="robot-dialog" @click.stop>
      <div class="dialog-header">
        <div class="header-title">
          <img src="../../assets/img/robot.png" alt="AI助手" class="robot-icon" />
          <span>AI智能助手</span>
        </div>
        <button class="close-btn" @click="closeDialog">×</button>
      </div>

      <div class="dialog-body">
        <div class="message-list" ref="messageListRef">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message-item', message.type]"
          >
            <div class="message-avatar">
              <img v-if="message.type === 'ai'" src="../../assets/img/robot.png" alt="AI" />
              <div v-else class="user-avatar">用</div>
            </div>
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ message.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <div class="input-area">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="2"
            placeholder="请输入您的问题..."
            @keyup.enter="handleSendMessage"
            :disabled="isLoading"
          />
          <button
            class="send-btn"
            @click="handleSendMessage"
            :disabled="!inputMessage.trim() || isLoading"
          >
            <span v-if="!isLoading">发送</span>
            <span v-else>发送中...</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  time: string
}

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

const messages = ref<Message[]>([
  {
    id: '1',
    type: 'ai',
    content:
      '您好！我是AI智能助手，可以帮助您解答关于地图数据和测绘质检的相关问题。请问有什么可以帮助您的吗？',
    time: formatTime(new Date()),
  },
])

const inputMessage = ref('')
const isLoading = ref(false)
const messageListRef = ref<HTMLElement>()

// 模拟AI回复的预设回答
const aiResponses = [
  '根据您的问题，我建议您可以通过以下方式处理...',
  '这是一个很好的问题。在测绘质检中，通常需要注意以下几个方面...',
  '基于地图数据分析，我认为您可以从这几个角度来考虑...',
  '关于这个问题，我需要更多的上下文信息。您能详细描述一下具体情况吗？',
  '根据我的分析，这种情况在实际工作中比较常见，建议采用以下解决方案...',
  '感谢您的提问。对于测绘数据的处理，我建议您参考相关的技术规范...',
]

function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

function closeDialog() {
  emit('update:visible', false)
  emit('close')
}

function handleOverlayClick() {
  closeDialog()
}

async function handleSendMessage() {
  if (!inputMessage.value.trim() || isLoading.value) return

  const userMessage: Message = {
    id: generateId(),
    type: 'user',
    content: inputMessage.value.trim(),
    time: formatTime(new Date()),
  }

  messages.value.push(userMessage)
  inputMessage.value = ''
  isLoading.value = true

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 模拟AI回复延迟
  setTimeout(
    () => {
      const aiMessage: Message = {
        id: generateId(),
        type: 'ai',
        content: aiResponses[Math.floor(Math.random() * aiResponses.length)],
        time: formatTime(new Date()),
      }

      messages.value.push(aiMessage)
      isLoading.value = false

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
      })
    },
    1000 + Math.random() * 2000,
  ) // 1-3秒随机延迟
}

function scrollToBottom() {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 监听对话框显示状态，显示时滚动到底部
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  },
)
</script>

<style scoped lang="scss">
.robot-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.robot-dialog {
  width: 500px;
  height: 600px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .header-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
    font-weight: 600;

    .robot-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.dialog-body {
  flex: 1;
  overflow: hidden;

  .message-list {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.message-item {
  display: flex;
  gap: 12px;

  &.user {
    flex-direction: row-reverse;

    .message-content {
      .message-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .message-time {
        text-align: right;
      }
    }
  }

  .message-avatar {
    flex-shrink: 0;
    width: 40px;
    height: 40px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-avatar {
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .message-content {
    flex: 1;
    max-width: calc(100% - 52px);

    .message-text {
      background: #f5f5f5;
      padding: 12px 16px;
      border-radius: 18px;
      word-wrap: break-word;
      line-height: 1.5;
      font-size: 14px;
    }

    .message-time {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
      padding: 0 16px;
    }
  }
}

.dialog-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  background: #fafafa;

  .input-area {
    display: flex;
    gap: 12px;
    align-items: flex-end;

    :deep(.el-textarea) {
      flex: 1;

      .el-textarea__inner {
        border-radius: 12px;
        border: 1px solid #ddd;
        resize: none;
        font-size: 14px;

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
      }
    }

    .send-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s;
      height: 40px;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .robot-dialog {
    width: 90vw;
    height: 80vh;
    margin: 0 20px;
  }
}
</style>
