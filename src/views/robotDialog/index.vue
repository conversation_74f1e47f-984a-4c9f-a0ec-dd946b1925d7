<template>
  <div class="robot-dialog-overlay" v-if="visible">
    <div class="robot-dialog" @click.stop>
      <div class="dialog-header">
        <div class="header-title">
          <img src="../../assets/img/robot.png" alt="AI助手" class="robot-icon" />
          <span>AI智能助手</span>
        </div>
        <button class="close-btn" @click="closeDialog">×</button>
      </div>

      <div class="dialog-body">
        <div class="message-list" ref="messageListRef">
          <div
            v-for="message in messages"
            :key="message.id"
            :class="['message-item', message.type]"
          >
            <div class="message-avatar">
              <img v-if="message.type === 'ai'" src="../../assets/img/robot.png" alt="AI" />
              <div v-else class="user-avatar">用</div>
            </div>
            <div class="message-content">
              <div class="message-text" :class="{ thinking: message.isThinking }">
                <span v-if="message.isThinking" class="thinking-dots">
                  质检助手正在思考
                  <span class="dots">
                    <span>.</span>
                    <span>.</span>
                    <span>.</span>
                  </span>
                </span>
                <div v-else>
                  <div class="message-answer" v-html="message.content"></div>
                  <!-- 文件来源展示 -->
                  <div v-if="message.sources && message.sources.length > 0" class="message-sources">
                    <div class="sources-title">参考文件：</div>
                    <div class="sources-list">
                      <div
                        v-for="source in message.sources"
                        :key="source.filePath"
                        class="source-item"
                        @click="handleFilePreview(source)"
                      >
                        <svg class="file-icon" viewBox="0 0 1024 1024" width="16" height="16">
                          <path
                            d="M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326z"
                            fill="currentColor"
                          />
                        </svg>
                        <div class="source-info">
                          <span class="source-name">{{ source.source }}</span>
                          <span class="source-time">{{ source.createTime }}</span>
                        </div>
                        <div class="source-actions" @click.stop>
                          <button
                            class="action-btn preview-btn"
                            @click="handleFilePreview(source)"
                            title="预览文件"
                          >
                            <svg viewBox="0 0 1024 1024" width="14" height="14">
                              <path
                                d="M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352zm0 64c-247.424 0-416 288-416 288s168.576 288 416 288 416-288 416-288-168.576-288-416-288z"
                                fill="currentColor"
                              />
                              <path
                                d="M512 256c141.385 0 256 114.615 256 256s-114.615 256-256 256-256-114.615-256-256 114.615-256 256-256zm0 64c-106.039 0-192 85.961-192 192s85.961 192 192 192 192-85.961 192-192-85.961-192-192-192z"
                                fill="currentColor"
                              />
                            </svg>
                          </button>
                          <button
                            class="action-btn download-btn"
                            @click="handleFileDownload(source)"
                            title="下载文件"
                          >
                            <svg viewBox="0 0 1024 1024" width="14" height="14">
                              <path
                                d="M505.7 661a8 8 0 0 0 12.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8z"
                                fill="currentColor"
                              />
                              <path
                                d="M878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
                                fill="currentColor"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="message-time">{{ message.time }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <div class="input-area">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="2"
            placeholder="请输入您的问题..."
            @keyup.enter="handleSendMessage"
            :disabled="isLoading"
          />
          <button
            class="send-btn"
            @click="handleButtonClick"
            :disabled="!isLoading && !inputMessage.trim()"
          >
            <span v-if="!isLoading">发送</span>
            <span v-else>停止等待</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { knowledgeSearch } from '@/api/common'
import { ref, nextTick, watch, onUnmounted } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'

interface Source {
  createTime: string
  filePath: string
  source: string
}

interface Message {
  id: string
  type: 'user' | 'ai'
  content: string
  time: string
  isThinking?: boolean
  sources?: Source[]
}

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

const messages = ref<Message[]>([
  {
    id: '1',
    type: 'ai',
    content:
      '您好！我是AI智能助手，可以帮助您解答关于地图数据和测绘质检的相关问题。请问有什么可以帮助您的吗？',
    time: formatTime(new Date()),
  },
])

const inputMessage = ref('')
const isLoading = ref(false)
const messageListRef = ref<HTMLElement>()
let currentTimeout: number | null = null

function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

function generateId(): string {
  return Date.now().toString() + Math.random().toString(36).substring(2, 11)
}

// 处理Markdown并防止XSS攻击
function parseMarkdownSafely(content: string): string {
  if (!content) return ''

  // 配置marked选项
  marked.setOptions({
    breaks: true, // 支持换行
    gfm: true, // 支持GitHub风格的Markdown
  })

  // 解析Markdown (同步方式)
  const htmlContent = marked.parse(content) as string

  // 使用DOMPurify清理HTML，防止XSS攻击
  const cleanHtml = DOMPurify.sanitize(htmlContent, {
    ALLOWED_TAGS: [
      'p',
      'br',
      'strong',
      'em',
      'u',
      'del',
      's',
      'strike',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'ul',
      'ol',
      'li',
      'blockquote',
      'pre',
      'code',
      'a',
      'img',
      'table',
      'thead',
      'tbody',
      'tr',
      'th',
      'td',
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'target'],
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur'],
  })

  return cleanHtml
}

function closeDialog() {
  emit('update:visible', false)
  emit('close')
}

async function handleSendMessage() {
  if (!inputMessage.value.trim() || isLoading.value) return

  const userMessage: Message = {
    id: generateId(),
    type: 'user',
    content: inputMessage.value.trim(),
    time: formatTime(new Date()),
  }

  messages.value.push(userMessage)
  inputMessage.value = ''
  isLoading.value = true

  // 添加思考中的消息
  const thinkingMessage: Message = {
    id: generateId(),
    type: 'ai',
    content: '',
    time: formatTime(new Date()),
    isThinking: true,
  }
  messages.value.push(thinkingMessage)

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 调用真实API
    const res = await knowledgeSearch({ question: userMessage.content })

    // 移除思考中的消息
    const thinkingIndex = messages.value.findIndex((msg) => msg.isThinking)
    if (thinkingIndex !== -1) {
      messages.value.splice(thinkingIndex, 1)
    }

    // 处理API响应
    if (res.code === 200 && res.data) {
      const rawContent =
        res.data.answerRemovedThinks || res.data.answer || '抱歉，我无法回答这个问题。'
      const aiMessage: Message = {
        id: generateId(),
        type: 'ai',
        content: parseMarkdownSafely(rawContent),
        time: formatTime(new Date()),
        sources: res.data.sources || [],
      }
      messages.value.push(aiMessage)
    } else {
      // API调用失败时的错误处理
      const errorMessage: Message = {
        id: generateId(),
        type: 'ai',
        content: '抱歉，服务暂时不可用，请稍后再试。',
        time: formatTime(new Date()),
        sources: [],
      }
      messages.value.push(errorMessage)
    }
  } catch (error) {
    console.error('API调用失败:', error)

    // 移除思考中的消息
    const thinkingIndex = messages.value.findIndex((msg) => msg.isThinking)
    if (thinkingIndex !== -1) {
      messages.value.splice(thinkingIndex, 1)
    }

    // 显示错误消息
    const errorMessage: Message = {
      id: generateId(),
      type: 'ai',
      content: '抱歉，网络连接出现问题，请检查网络后重试。',
      time: formatTime(new Date()),
      sources: [],
    }
    messages.value.push(errorMessage)
  } finally {
    isLoading.value = false
    currentTimeout = null

    // 滚动到底部
    await nextTick()
    scrollToBottom()
  }
}

function handleStopWaiting() {
  if (currentTimeout) {
    clearTimeout(currentTimeout)
    currentTimeout = null
  }

  // 移除思考中的消息
  const thinkingIndex = messages.value.findIndex((msg) => msg.isThinking)
  if (thinkingIndex !== -1) {
    messages.value.splice(thinkingIndex, 1)
  }

  isLoading.value = false
}

function handleButtonClick() {
  if (isLoading.value) {
    handleStopWaiting()
  } else {
    handleSendMessage()
  }
}

function handleFilePreview(source: Source) {
  // 构建文件预览URL
  const baseUrl = import.meta.env.VITE_APP_BASE_URL || window.location.origin
  const previewUrl = `${baseUrl}${source.filePath}`

  // 在新标签页中打开预览
  window.open(previewUrl, '_blank')
}

function handleFileDownload(source: Source) {
  // 构建文件下载URL
  const baseUrl = import.meta.env.VITE_APP_BASE_URL || window.location.origin
  const downloadUrl = `${baseUrl}${source.filePath}`

  // 创建临时下载链接
  const link = document.createElement('a')
  link.href = downloadUrl
  link.download = source.source
  link.target = '_blank'

  // 触发下载
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

function scrollToBottom() {
  if (messageListRef.value) {
    messageListRef.value.scrollTop = messageListRef.value.scrollHeight
  }
}

// 监听对话框显示状态，显示时滚动到底部
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  },
)

// 组件卸载时清理定时器
onUnmounted(() => {
  if (currentTimeout) {
    clearTimeout(currentTimeout)
    currentTimeout = null
  }
})
</script>

<style scoped lang="scss">
.robot-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.robot-dialog {
  width: 650px;
  height: 750px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(0deg, rgba(4, 37, 101, 0.9), rgba(19, 59, 137, 0.9));
  color: white;

  .header-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 600;

    .robot-icon {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.dialog-body {
  flex: 1;
  overflow: hidden;

  .message-list {
    height: 100%;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.message-item {
  display: flex;
  gap: 12px;

  &.user {
    flex-direction: row-reverse;

    .message-content {
      align-items: flex-end;

      .message-text {
        background: linear-gradient(0deg, rgba(4, 37, 101, 0.9), rgba(19, 59, 137, 0.9));
        color: white;
      }

      .message-time {
        text-align: right;
      }
    }
  }

  .message-avatar {
    flex-shrink: 0;
    width: 40px;
    height: 40px;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-avatar {
      width: 100%;
      height: 100%;
      background: linear-gradient(0deg, rgba(4, 37, 101, 0.9), rgba(19, 59, 137, 0.9));
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .message-content {
    flex: 1;
    max-width: calc(100% - 52px);
    display: flex;
    flex-direction: column;

    .message-text {
      background: #f5f5f5;
      padding: 14px 18px;
      border-radius: 18px;
      word-wrap: break-word;
      line-height: 1.6;
      font-size: 16px;
      display: inline-block;
      max-width: fit-content;

      &.thinking {
        background: #f0f8ff;
        color: #666;
        font-style: italic;
      }

      // Markdown样式
      :deep(h1),
      :deep(h2),
      :deep(h3),
      :deep(h4),
      :deep(h5),
      :deep(h6) {
        margin: 12px 0 8px 0;
        font-weight: 600;
        line-height: 1.4;
      }

      :deep(h1) {
        font-size: 20px;
      }
      :deep(h2) {
        font-size: 18px;
      }
      :deep(h3) {
        font-size: 17px;
      }
      :deep(h4) {
        font-size: 16px;
      }
      :deep(h5) {
        font-size: 15px;
      }
      :deep(h6) {
        font-size: 14px;
      }

      :deep(p) {
        margin: 8px 0;
        &:first-child {
          margin-top: 0;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }

      :deep(ul),
      :deep(ol) {
        margin: 8px 0;
        padding-left: 20px;
      }

      :deep(li) {
        margin: 4px 0;
      }

      :deep(blockquote) {
        margin: 12px 0;
        padding: 8px 12px;
        border-left: 3px solid rgba(4, 37, 101, 0.3);
        background: rgba(4, 37, 101, 0.05);
        border-radius: 4px;
      }

      :deep(pre) {
        margin: 12px 0;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        overflow-x: auto;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      :deep(code) {
        padding: 2px 4px;
        background: #f8f9fa;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      :deep(pre code) {
        padding: 0;
        background: transparent;
      }

      :deep(a) {
        color: rgba(4, 37, 101, 0.8);
        text-decoration: none;

        &:hover {
          color: rgba(4, 37, 101, 1);
          text-decoration: underline;
        }
      }

      :deep(strong) {
        font-weight: 600;
      }

      :deep(em) {
        font-style: italic;
      }

      :deep(table) {
        margin: 12px 0;
        border-collapse: collapse;
        width: 100%;
      }

      :deep(th),
      :deep(td) {
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        text-align: left;
      }

      :deep(th) {
        background: #f8f9fa;
        font-weight: 600;
      }
    }

    .thinking-dots {
      display: flex;
      align-items: center;
      gap: 4px;

      .dots {
        display: flex;
        gap: 2px;

        span {
          animation: thinking 1.4s infinite ease-in-out;

          &:nth-child(1) {
            animation-delay: 0s;
          }
          &:nth-child(2) {
            animation-delay: 0.2s;
          }
          &:nth-child(3) {
            animation-delay: 0.4s;
          }
        }
      }
    }

    .message-time {
      font-size: 13px;
      color: #999;
      margin-top: 6px;
      padding: 0 18px;
    }

    .message-sources {
      margin-top: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 3px solid rgba(4, 37, 101, 0.3);

      .sources-title {
        font-size: 14px;
        font-weight: 600;
        color: #666;
        margin-bottom: 8px;
      }

      .sources-list {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .source-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: #fff;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        transition: all 0.2s;

        &:hover {
          border-color: rgba(4, 37, 101, 0.3);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }

        .file-icon {
          color: rgba(4, 37, 101, 0.7);
          flex-shrink: 0;
        }

        .source-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 2px;

          .source-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            line-height: 1.2;
          }

          .source-time {
            font-size: 12px;
            color: #999;
          }
        }

        .source-actions {
          display: flex;
          gap: 4px;
          opacity: 0;
          transition: opacity 0.2s;
        }

        &:hover .source-actions {
          opacity: 1;
        }

        .action-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 28px;
          height: 28px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;

          &.preview-btn {
            background: rgba(4, 37, 101, 0.1);
            color: rgba(4, 37, 101, 0.8);

            &:hover {
              background: rgba(4, 37, 101, 0.2);
              color: rgba(4, 37, 101, 1);
            }
          }

          &.download-btn {
            background: rgba(40, 167, 69, 0.1);
            color: rgba(40, 167, 69, 0.8);

            &:hover {
              background: rgba(40, 167, 69, 0.2);
              color: rgba(40, 167, 69, 1);
            }
          }

          svg {
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

.dialog-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  background: #fafafa;

  .input-area {
    display: flex;
    gap: 12px;
    align-items: flex-end;

    :deep(.el-textarea) {
      flex: 1;

      .el-textarea__inner {
        border-radius: 12px;
        border: 1px solid rgba(102, 127, 178, 0.5);
        resize: none;
        font-size: 16px;
        color: #333;
        background-color: #fff;

        &:focus {
          border-color: #215998;
          box-shadow: 0 0 0 2px rgba(33, 89, 152, 0.2);
        }

        &::placeholder {
          color: #999;
        }
      }
    }

    .send-btn {
      background: linear-gradient(0deg, rgba(4, 37, 101, 0.9), rgba(19, 59, 137, 0.9));
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.2s;
      height: 44px;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(33, 89, 152, 0.4);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }
}

// 思考动画
@keyframes thinking {
  0%,
  80%,
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .robot-dialog {
    width: 90vw;
    height: 80vh;
    margin: 0 20px;
  }
}
</style>
