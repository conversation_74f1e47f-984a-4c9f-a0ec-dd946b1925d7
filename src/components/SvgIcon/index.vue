<template>
  <svg :class="svgClass" aria-hidden="true">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义 props 类型
interface Props {
  iconClass: string
  className?: string
  color?: string
}

// 使用 defineProps 接收 props
const props = defineProps<Props>()

// 计算生成的 xlink:href
const iconName = computed(() => `#icon-${props.iconClass}`)

// 计算 SVG 的 class
const svgClass = computed(() => {
  return props.className ? `svg-icon ${props.className}` : 'svg-icon'
})
</script>

<style scoped lang="scss">
.sub-el-icon,
.nav-icon {
  display: inline-block;
  font-size: 15px;
  margin-right: 12px;
  position: relative;
}

.svg-icon {
  width: 1em;
  height: 1em;
  position: relative;
  fill: currentColor;
  vertical-align: -2px;
}
</style>
