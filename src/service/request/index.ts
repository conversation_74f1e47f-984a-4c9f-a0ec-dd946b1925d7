import axios from 'axios'
import type {
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosRequestConfig,
  AxiosError,
  AxiosResponse,
} from 'axios'
import { CodeConfig } from './codeConfig'
import type { ResponseModel, UploadFileItemModel, UploadRequestConfig } from './type'

// import { localCache } from '@/utils/cache'
import { LOGIN_TOKEN } from '@/global/constants'
import { localCache } from '@/utils/cache'
import { ElMessage } from 'element-plus'

// const token = localStorage.getItem(LOGIN_TOKEN)
const token = localCache.getCache(LOGIN_TOKEN)

class HttpRequest {
  service: AxiosInstance

  constructor() {
    this.service = axios.create({
      baseURL: import.meta.env.VITE_APP_BASE_API,
      timeout: 10 * 1000,
    })

    this.service.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        /**
         * set your config
         */
        if (token) {
          config.headers['Authorization'] = 'Bearer ' + token
        }
        console.log(config)
        // get请求映射params参数
        if (config.method === 'get' && config.params) {
          let url = config.url + '?'
          for (const propName of Object.keys(config.params)) {
            const value = config.params[propName]
            const part = encodeURIComponent(propName) + '='
            if (value !== null && typeof value !== 'undefined' && value !== '') {
              if (typeof value === 'object') {
                for (const key of Object.keys(value)) {
                  const params = propName + '[' + key + ']'
                  const subPart = encodeURIComponent(params) + '='
                  url += subPart + encodeURIComponent(value[key]) + '&'
                }
              } else {
                url += part + encodeURIComponent(value) + '&'
              }
            }
          }
          url = url.slice(0, -1)
          config.params = {}
          config.url = url
        }
        return config
      },
      (error: AxiosError) => {
        console.log('requestError: ', error)
        return Promise.reject(error)
      },
      {
        synchronous: false,
        // runWhen: (config: InternalAxiosRequestConfig) => {
        runWhen: () => {
          // do something
          // console.log(config)
          // if return true, axios will execution interceptor method
          return true
        },
      },
    )

    this.service.interceptors.response.use(
      (response: AxiosResponse<ResponseModel>): AxiosResponse['data'] => {
        const { data } = response
        const { code } = data
        if (code) {
          if (code != CodeConfig.success) {
            switch (code) {
              case CodeConfig.notFound:
                // the method to handle this code
                break
              case CodeConfig.noPermission:
                // the method to handle this code
                break
              case CodeConfig.unauthorized:
                // the method to handle this code
                ElMessage({ message: data.msg, type: 'error' })
                break
              case CodeConfig.serverError:
                ElMessage({ message: data.msg, type: 'error' })
                // the method to handle this code
                break
              default:
                break
            }
            return Promise.reject(data.msg)
          } else {
            return data
          }
        } else {
          return Promise.reject('Error! code missing!')
        }
      },
      (error: any) => {
        return Promise.reject(error)
      },
    )
  }

  request<T = any>(config: AxiosRequestConfig): Promise<ResponseModel<T>> {
    /**
     * TODO: execute other methods according to config
     */
    return new Promise((resolve, reject) => {
      try {
        this.service
          .request<ResponseModel<T>>(config)
          .then((res: AxiosResponse['data']) => {
            resolve(res as ResponseModel<T>)
          })
          .catch((err) => {
            reject(err)
          })
      } catch (err) {
        return Promise.reject(err)
      }
    })
  }

  get<T = any>(config: AxiosRequestConfig): Promise<ResponseModel<T>> {
    return this.request({ method: 'GET', ...config })
  }
  post<T = any>(config: AxiosRequestConfig): Promise<ResponseModel<T>> {
    return this.request({ method: 'POST', ...config })
  }
  put<T = any>(config: AxiosRequestConfig): Promise<ResponseModel<T>> {
    return this.request({ method: 'PUT', ...config })
  }
  delete<T = any>(config: AxiosRequestConfig): Promise<ResponseModel<T>> {
    return this.request({ method: 'DELETE', ...config })
  }
  upload<T = string>(
    fileItem: UploadFileItemModel,
    config?: UploadRequestConfig,
  ): Promise<ResponseModel<T>> | null {
    if (!import.meta.env.VITE_UPLOAD_URL) return null

    const fd = new FormData()
    fd.append(fileItem.name, fileItem.value)
    let configCopy: UploadRequestConfig
    if (!config) {
      configCopy = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    } else {
      config.headers!['Content-Type'] = 'multipart/form-data'
      configCopy = config
    }
    return this.request({ url: import.meta.env.VITE_UPLOAD_URL, data: fd, ...configCopy })
  }
}

const httpRequest = new HttpRequest()
export default httpRequest
