{"name": "resource-library-screen", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "prod": "vite --mode production", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@mapbox/mapbox-gl-draw": "^1.5.0", "@terraformer/wkt": "^2.2.1", "@turf/turf": "^7.2.0", "@types/mapbox__mapbox-gl-draw": "^1.4.9", "@vueuse/core": "^13.4.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "element-plus": "^2.10.2", "mapbox-gl": "^3.13.0", "normalize.css": "^8.0.1", "pinia": "^3.0.1", "sass": "^1.89.2", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/terraformer__wkt": "^2.0.3", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "postcss-px-to-viewport": "^1.1.1", "prettier": "3.5.3", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}